[{"C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\i18n\\index.js": "4", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\LoginPage.js": "5", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ForgetPasswordPage.js": "6", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\NotFoundPage.js": "7", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetPasswordPage.js": "8", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\HomePage.js": "9", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetSuccessPage.js": "10", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\FeedbackPage.js": "11", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\CoursesPage.js": "12", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProfilePage.js": "13", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentLandingPage.js": "14", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProgramsPage.js": "15", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ModulePage.js": "16", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentProgramPage.js": "17", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SeanceFormateurPage.js": "18", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ContenusPage.js": "19", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\WhiteboardPage.js": "20", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\EditProfilePage.js": "21", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionPage.js": "22", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\BuildProgramOverviewPage.js": "23", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\VerifyAccountPage.js": "24", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionDetail.js": "25", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Main.js": "26", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Auth.js": "27", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\UsersPages.js": "28", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddModuleView.js": "29", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddUserView.js": "30", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddProgramList.js": "31", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\UserList.js": "32", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddQuizForm.js": "33", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\PlayQuizPage.js": "34", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditProgramView.js": "35", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SeanceFormateurList.js": "36", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AnimerSeanceView.js": "37", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\BuildProgramView.js": "38", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFormateurView.js": "39", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ModuleList.js": "40", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditQuizForm.js": "41", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AdminDashboard.js": "42", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionFeedbackList.js": "43", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CreateurDashboard.js": "44", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtablissementDashboard.js": "45", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtudiantDashboard.js": "46", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\FormateurDashboard.js": "47", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\JitsiRoom.js": "48", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\index.js": "49", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastError.js": "50", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CourseList.js": "51", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastSuccess.js": "52", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddCourseView.js": "53", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ProgramList.js": "54", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ContenusList.js": "55", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Whiteboard.js": "56", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddContenusView.js": "57", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionList.js": "58", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\LanguageSelectorWithFlags.js": "59", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionView.js": "60", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScrollToTopButton.js": "61", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Session2ChatPopup.js": "62", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\toastError.js": "63", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\feedbackService.js": "64", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScoreReveal.js": "65", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\authUtils.js": "66", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\constants\\sideBarData.js": "67", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\FeedbackFormateur.js": "68", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\seancefeedbacklist.js": "69", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFeedback.js": "70", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\Chatbot.js": "71", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionFeedback.js": "72", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\QuickSuggestions.js": "73", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\languageService.js": "74"}, {"size": 610, "mtime": 1747831852695, "results": "75", "hashOfConfig": "76"}, {"size": 11448, "mtime": 1753888463600, "results": "77", "hashOfConfig": "76"}, {"size": 375, "mtime": 1745438071501, "results": "78", "hashOfConfig": "76"}, {"size": 656, "mtime": 1752859285013, "results": "79", "hashOfConfig": "76"}, {"size": 5622, "mtime": 1751990678109, "results": "80", "hashOfConfig": "76"}, {"size": 3723, "mtime": 1750871433201, "results": "81", "hashOfConfig": "76"}, {"size": 455, "mtime": 1750865324399, "results": "82", "hashOfConfig": "76"}, {"size": 6151, "mtime": 1750873153170, "results": "83", "hashOfConfig": "76"}, {"size": 1687, "mtime": 1753356431070, "results": "84", "hashOfConfig": "76"}, {"size": 694, "mtime": 1747594139572, "results": "85", "hashOfConfig": "76"}, {"size": 25936, "mtime": 1752342963093, "results": "86", "hashOfConfig": "76"}, {"size": 420, "mtime": 1747844523996, "results": "87", "hashOfConfig": "76"}, {"size": 15067, "mtime": 1753136360471, "results": "88", "hashOfConfig": "76"}, {"size": 2240, "mtime": 1750873583618, "results": "89", "hashOfConfig": "76"}, {"size": 491, "mtime": 1747844524003, "results": "90", "hashOfConfig": "76"}, {"size": 850, "mtime": 1750872193880, "results": "91", "hashOfConfig": "76"}, {"size": 4575, "mtime": 1750883025232, "results": "92", "hashOfConfig": "76"}, {"size": 3424, "mtime": 1753700299624, "results": "93", "hashOfConfig": "76"}, {"size": 422, "mtime": 1747844523996, "results": "94", "hashOfConfig": "76"}, {"size": 478, "mtime": 1751986578114, "results": "95", "hashOfConfig": "76"}, {"size": 55698, "mtime": 1751985929377, "results": "96", "hashOfConfig": "76"}, {"size": 1026, "mtime": 1750873436634, "results": "97", "hashOfConfig": "76"}, {"size": 7665, "mtime": 1753034959934, "results": "98", "hashOfConfig": "76"}, {"size": 15134, "mtime": 1752581112327, "results": "99", "hashOfConfig": "76"}, {"size": 4159, "mtime": 1750777638880, "results": "100", "hashOfConfig": "76"}, {"size": 19519, "mtime": 1753294211583, "results": "101", "hashOfConfig": "76"}, {"size": 153, "mtime": 1745582315206, "results": "102", "hashOfConfig": "76"}, {"size": 284, "mtime": 1750858318977, "results": "103", "hashOfConfig": "76"}, {"size": 2909, "mtime": 1750887085865, "results": "104", "hashOfConfig": "76"}, {"size": 11933, "mtime": 1752069953530, "results": "105", "hashOfConfig": "76"}, {"size": 1693, "mtime": 1750850985128, "results": "106", "hashOfConfig": "76"}, {"size": 25111, "mtime": 1751985930792, "results": "107", "hashOfConfig": "76"}, {"size": 11596, "mtime": 1750887959421, "results": "108", "hashOfConfig": "76"}, {"size": 6477, "mtime": 1750872851488, "results": "109", "hashOfConfig": "76"}, {"size": 12740, "mtime": 1749741251393, "results": "110", "hashOfConfig": "76"}, {"size": 5969, "mtime": 1753704624495, "results": "111", "hashOfConfig": "76"}, {"size": 22810, "mtime": 1753375441494, "results": "112", "hashOfConfig": "76"}, {"size": 8204, "mtime": 1750890990379, "results": "113", "hashOfConfig": "76"}, {"size": 5733, "mtime": 1752069593152, "results": "114", "hashOfConfig": "76"}, {"size": 3301, "mtime": 1752068170287, "results": "115", "hashOfConfig": "76"}, {"size": 11954, "mtime": 1752341829288, "results": "116", "hashOfConfig": "76"}, {"size": 22778, "mtime": 1753356431075, "results": "117", "hashOfConfig": "76"}, {"size": 19233, "mtime": 1754063321914, "results": "118", "hashOfConfig": "76"}, {"size": 22730, "mtime": 1753356431232, "results": "119", "hashOfConfig": "76"}, {"size": 10045, "mtime": 1753136361210, "results": "120", "hashOfConfig": "76"}, {"size": 9460, "mtime": 1753136361257, "results": "121", "hashOfConfig": "76"}, {"size": 14064, "mtime": 1753356431325, "results": "122", "hashOfConfig": "76"}, {"size": 665, "mtime": 1753136360410, "results": "123", "hashOfConfig": "76"}, {"size": 56, "mtime": 1750504542636, "results": "124", "hashOfConfig": "76"}, {"size": 751, "mtime": 1748635026074, "results": "125", "hashOfConfig": "76"}, {"size": 2964, "mtime": 1752068241528, "results": "126", "hashOfConfig": "76"}, {"size": 620, "mtime": 1747594139559, "results": "127", "hashOfConfig": "76"}, {"size": 1393, "mtime": 1750866339097, "results": "128", "hashOfConfig": "76"}, {"size": 3083, "mtime": 1751985930466, "results": "129", "hashOfConfig": "76"}, {"size": 4753, "mtime": 1751985930121, "results": "130", "hashOfConfig": "76"}, {"size": 10232, "mtime": 1753035018285, "results": "131", "hashOfConfig": "76"}, {"size": 4696, "mtime": 1751985929811, "results": "132", "hashOfConfig": "76"}, {"size": 25558, "mtime": 1753904297260, "results": "133", "hashOfConfig": "76"}, {"size": 7155, "mtime": 1751298764731, "results": "134", "hashOfConfig": "76"}, {"size": 5299, "mtime": 1750884972392, "results": "135", "hashOfConfig": "76"}, {"size": 897, "mtime": 1747061904678, "results": "136", "hashOfConfig": "76"}, {"size": 23129, "mtime": 1753356169008, "results": "137", "hashOfConfig": "76"}, {"size": 5376, "mtime": 1748517649309, "results": "138", "hashOfConfig": "76"}, {"size": 5485, "mtime": 1753034961627, "results": "139", "hashOfConfig": "76"}, {"size": 890, "mtime": 1749741250195, "results": "140", "hashOfConfig": "76"}, {"size": 4625, "mtime": 1753034977418, "results": "141", "hashOfConfig": "76"}, {"size": 2175, "mtime": 1753370053964, "results": "142", "hashOfConfig": "76"}, {"size": 12989, "mtime": 1753365132448, "results": "143", "hashOfConfig": "76"}, {"size": 12275, "mtime": 1753967365133, "results": "144", "hashOfConfig": "76"}, {"size": 23365, "mtime": 1753369624191, "results": "145", "hashOfConfig": "76"}, {"size": 10857, "mtime": 1753354385496, "results": "146", "hashOfConfig": "76"}, {"size": 53761, "mtime": 1754064502687, "results": "147", "hashOfConfig": "76"}, {"size": 1223, "mtime": 1751656822978, "results": "148", "hashOfConfig": "76"}, {"size": 2878, "mtime": 1751659811949, "results": "149", "hashOfConfig": "76"}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16w30kw", {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\App.js", ["372", "373"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\i18n\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ForgetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\NotFoundPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetSuccessPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\FeedbackPage.js", ["374", "375", "376"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\CoursesPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProfilePage.js", ["377"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentLandingPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProgramsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ModulePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentProgramPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SeanceFormateurPage.js", ["378"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ContenusPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\WhiteboardPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\EditProfilePage.js", ["379"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\BuildProgramOverviewPage.js", ["380"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\VerifyAccountPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Main.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\UsersPages.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddModuleView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddUserView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddProgramList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\UserList.js", ["381", "382", "383"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddQuizForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\PlayQuizPage.js", ["384", "385"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditProgramView.js", ["386"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SeanceFormateurList.js", ["387"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AnimerSeanceView.js", ["388", "389", "390"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\BuildProgramView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFormateurView.js", ["391"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ModuleList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditQuizForm.js", ["392", "393"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AdminDashboard.js", ["394", "395"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionFeedbackList.js", ["396", "397", "398"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CreateurDashboard.js", ["399", "400"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtablissementDashboard.js", ["401"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtudiantDashboard.js", ["402"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\FormateurDashboard.js", ["403"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\JitsiRoom.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastError.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CourseList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastSuccess.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddCourseView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ProgramList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ContenusList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Whiteboard.js", ["404", "405"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddContenusView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionList.js", ["406", "407", "408", "409", "410", "411", "412"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\LanguageSelectorWithFlags.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionView.js", ["413"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScrollToTopButton.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Session2ChatPopup.js", ["414", "415", "416"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\toastError.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\feedbackService.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScoreReveal.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\authUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\constants\\sideBarData.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\FeedbackFormateur.js", ["417"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\seancefeedbacklist.js", ["418"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFeedback.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\Chatbot.js", ["419"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionFeedback.js", ["420", "421"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\QuickSuggestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\languageService.js", [], [], {"ruleId": "422", "severity": 1, "message": "423", "line": 4, "column": 10, "nodeType": "424", "messageId": "425", "endLine": 4, "endColumn": 24}, {"ruleId": "422", "severity": 1, "message": "426", "line": 52, "column": 8, "nodeType": "424", "messageId": "425", "endLine": 52, "endColumn": 21}, {"ruleId": "422", "severity": 1, "message": "427", "line": 19, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 19, "endColumn": 12}, {"ruleId": "422", "severity": 1, "message": "428", "line": 30, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 30, "endColumn": 10}, {"ruleId": "429", "severity": 1, "message": "430", "line": 202, "column": 5, "nodeType": "431", "messageId": "432", "endLine": 224, "endColumn": 6}, {"ruleId": "433", "severity": 1, "message": "434", "line": 170, "column": 6, "nodeType": "435", "endLine": 170, "endColumn": 20, "suggestions": "436"}, {"ruleId": "433", "severity": 1, "message": "437", "line": 42, "column": 6, "nodeType": "435", "endLine": 42, "endColumn": 17, "suggestions": "438"}, {"ruleId": "422", "severity": 1, "message": "439", "line": 171, "column": 19, "nodeType": "424", "messageId": "425", "endLine": 171, "endColumn": 23}, {"ruleId": "433", "severity": 1, "message": "440", "line": 69, "column": 6, "nodeType": "435", "endLine": 69, "endColumn": 17, "suggestions": "441"}, {"ruleId": "422", "severity": 1, "message": "442", "line": 37, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 37, "endColumn": 10}, {"ruleId": "433", "severity": 1, "message": "443", "line": 83, "column": 6, "nodeType": "435", "endLine": 83, "endColumn": 8, "suggestions": "444"}, {"ruleId": "422", "severity": 1, "message": "445", "line": 241, "column": 9, "nodeType": "424", "messageId": "425", "endLine": 241, "endColumn": 23}, {"ruleId": "433", "severity": 1, "message": "446", "line": 56, "column": 6, "nodeType": "435", "endLine": 56, "endColumn": 23, "suggestions": "447"}, {"ruleId": "422", "severity": 1, "message": "448", "line": 68, "column": 9, "nodeType": "424", "messageId": "425", "endLine": 68, "endColumn": 14}, {"ruleId": "433", "severity": 1, "message": "449", "line": 77, "column": 6, "nodeType": "435", "endLine": 77, "endColumn": 17, "suggestions": "450"}, {"ruleId": "422", "severity": 1, "message": "451", "line": 7, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 7, "endColumn": 8}, {"ruleId": "422", "severity": 1, "message": "452", "line": 47, "column": 10, "nodeType": "424", "messageId": "425", "endLine": 47, "endColumn": 17}, {"ruleId": "422", "severity": 1, "message": "453", "line": 56, "column": 10, "nodeType": "424", "messageId": "425", "endLine": 56, "endColumn": 29}, {"ruleId": "422", "severity": 1, "message": "454", "line": 56, "column": 31, "nodeType": "424", "messageId": "425", "endLine": 56, "endColumn": 53}, {"ruleId": "422", "severity": 1, "message": "455", "line": 18, "column": 10, "nodeType": "424", "messageId": "425", "endLine": 18, "endColumn": 13}, {"ruleId": "422", "severity": 1, "message": "428", "line": 14, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 14, "endColumn": 10}, {"ruleId": "422", "severity": 1, "message": "456", "line": 15, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 15, "endColumn": 10}, {"ruleId": "422", "severity": 1, "message": "457", "line": 22, "column": 8, "nodeType": "424", "messageId": "425", "endLine": 22, "endColumn": 21}, {"ruleId": "422", "severity": 1, "message": "458", "line": 23, "column": 8, "nodeType": "424", "messageId": "425", "endLine": 23, "endColumn": 19}, {"ruleId": "422", "severity": 1, "message": "459", "line": 1, "column": 17, "nodeType": "424", "messageId": "425", "endLine": 1, "endColumn": 26}, {"ruleId": "422", "severity": 1, "message": "428", "line": 14, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 14, "endColumn": 10}, {"ruleId": "433", "severity": 1, "message": "460", "line": 42, "column": 6, "nodeType": "435", "endLine": 42, "endColumn": 17, "suggestions": "461"}, {"ruleId": "422", "severity": 1, "message": "462", "line": 10, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 10, "endColumn": 9}, {"ruleId": "422", "severity": 1, "message": "463", "line": 75, "column": 10, "nodeType": "424", "messageId": "425", "endLine": 75, "endColumn": 15}, {"ruleId": "422", "severity": 1, "message": "464", "line": 31, "column": 20, "nodeType": "424", "messageId": "425", "endLine": 31, "endColumn": 31}, {"ruleId": "422", "severity": 1, "message": "465", "line": 18, "column": 8, "nodeType": "424", "messageId": "425", "endLine": 18, "endColumn": 22}, {"ruleId": "422", "severity": 1, "message": "466", "line": 16, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 16, "endColumn": 8}, {"ruleId": "422", "severity": 1, "message": "467", "line": 12, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 12, "endColumn": 9}, {"ruleId": "422", "severity": 1, "message": "468", "line": 19, "column": 8, "nodeType": "424", "messageId": "425", "endLine": 19, "endColumn": 21}, {"ruleId": "422", "severity": 1, "message": "469", "line": 30, "column": 8, "nodeType": "424", "messageId": "425", "endLine": 30, "endColumn": 27}, {"ruleId": "422", "severity": 1, "message": "470", "line": 43, "column": 10, "nodeType": "424", "messageId": "425", "endLine": 43, "endColumn": 33}, {"ruleId": "422", "severity": 1, "message": "471", "line": 43, "column": 35, "nodeType": "424", "messageId": "425", "endLine": 43, "endColumn": 61}, {"ruleId": "422", "severity": 1, "message": "472", "line": 44, "column": 10, "nodeType": "424", "messageId": "425", "endLine": 44, "endColumn": 40}, {"ruleId": "422", "severity": 1, "message": "473", "line": 44, "column": 42, "nodeType": "424", "messageId": "425", "endLine": 44, "endColumn": 75}, {"ruleId": "433", "severity": 1, "message": "474", "line": 71, "column": 6, "nodeType": "435", "endLine": 71, "endColumn": 8, "suggestions": "475"}, {"ruleId": "422", "severity": 1, "message": "476", "line": 162, "column": 9, "nodeType": "424", "messageId": "425", "endLine": 162, "endColumn": 30}, {"ruleId": "433", "severity": 1, "message": "434", "line": 35, "column": 6, "nodeType": "435", "endLine": 35, "endColumn": 8, "suggestions": "477"}, {"ruleId": "422", "severity": 1, "message": "478", "line": 9, "column": 10, "nodeType": "424", "messageId": "425", "endLine": 9, "endColumn": 14}, {"ruleId": "422", "severity": 1, "message": "479", "line": 9, "column": 16, "nodeType": "424", "messageId": "425", "endLine": 9, "endColumn": 19}, {"ruleId": "422", "severity": 1, "message": "480", "line": 40, "column": 23, "nodeType": "424", "messageId": "425", "endLine": 40, "endColumn": 37}, {"ruleId": "433", "severity": 1, "message": "481", "line": 76, "column": 6, "nodeType": "435", "endLine": 76, "endColumn": 29, "suggestions": "482"}, {"ruleId": "433", "severity": 1, "message": "483", "line": 52, "column": 6, "nodeType": "435", "endLine": 52, "endColumn": 16, "suggestions": "484"}, {"ruleId": "433", "severity": 1, "message": "485", "line": 35, "column": 6, "nodeType": "435", "endLine": 35, "endColumn": 20, "suggestions": "486"}, {"ruleId": "422", "severity": 1, "message": "428", "line": 30, "column": 3, "nodeType": "424", "messageId": "425", "endLine": 30, "endColumn": 10}, {"ruleId": "429", "severity": 1, "message": "430", "line": 193, "column": 5, "nodeType": "431", "messageId": "432", "endLine": 220, "endColumn": 6}, "no-unused-vars", "'useTranslation' is defined but never used.", "Identifier", "unusedVar", "'SessionDetail' is defined but never used.", "'FormGroup' is defined but never used.", "'Divider' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["487"], "React Hook useEffect has missing dependencies: 'fetchSeances' and 'fetchSessionDetails'. Either include them or remove the dependency array.", ["488"], "'gray' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 't'. Either include it or remove the dependency array.", ["489"], "'Refresh' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["490"], "'getAvatarColor' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["491"], "'total' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'contenusByCourse' and 'coursesByModule'. Either include them or remove the dependency array.", ["492"], "'Stack' is defined but never used.", "'prevTab' is assigned a value but never used.", "'showFeedbackSidebar' is assigned a value but never used.", "'setShowFeedbackSidebar' is assigned a value but never used.", "'Eye' is defined but never used.", "'Tooltip' is defined but never used.", "'EventBusyIcon' is defined but never used.", "'ArchiveIcon' is defined but never used.", "'useEffect' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'reloadFeedbacks'. Either include it or remove the dependency array.", ["493"], "'Avatar' is defined but never used.", "'error' is assigned a value but never used.", "'setStudents' is assigned a value but never used.", "'TrendingUpIcon' is defined but never used.", "'Paper' is defined but never used.", "'Button' is defined but never used.", "'ColorLensIcon' is defined but never used.", "'SessionFeedbackList' is defined but never used.", "'openSessionFeedbackList' is assigned a value but never used.", "'setOpenSessionFeedbackList' is assigned a value but never used.", "'selectedSessionForFeedbackList' is assigned a value but never used.", "'setSelectedSessionForFeedbackList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSessions'. Either include it or remove the dependency array.", ["494"], "'handleDownloadPreview' is assigned a value but never used.", ["495"], "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'setShowTooltip' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStudents'. Either include it or remove the dependency array.", ["496"], "React Hook useEffect has a missing dependency: 'reloadFeedbacks'. Either include it or remove the dependency array.", ["497"], "React Hook useEffect has missing dependencies: 'messages.length' and 'welcomeMessages'. Either include them or remove the dependency array.", ["498"], {"desc": "499", "fix": "500"}, {"desc": "501", "fix": "502"}, {"desc": "503", "fix": "504"}, {"desc": "505", "fix": "506"}, {"desc": "507", "fix": "508"}, {"desc": "509", "fix": "510"}, {"desc": "511", "fix": "512"}, {"desc": "513", "fix": "514"}, {"desc": "515", "fix": "516"}, {"desc": "517", "fix": "518"}, {"desc": "519", "fix": "520"}, {"desc": "521", "fix": "522"}, "Update the dependencies array to be: [id, navigate, t]", {"range": "523", "text": "524"}, "Update the dependencies array to be: [fetchSeances, fetchSessionDetails, sessionId]", {"range": "525", "text": "526"}, "Update the dependencies array to be: [programId, t]", {"range": "527", "text": "528"}, "Update the dependencies array to be: [fetchUsers]", {"range": "529", "text": "530"}, "Update the dependencies array to be: [timeLeft, score, handleSubmit]", {"range": "531", "text": "532"}, "Update the dependencies array to be: [contenusByCourse, coursesByModule, programId]", {"range": "533", "text": "534"}, "Update the dependencies array to be: [reloadFeedbacks, sessionId]", {"range": "535", "text": "536"}, "Update the dependencies array to be: [fetchSessions]", {"range": "537", "text": "538"}, "Update the dependencies array to be: [t]", {"range": "539", "text": "540"}, "Update the dependencies array to be: [formateurId, loadStudents, seanceId]", {"range": "541", "text": "542"}, "Update the dependencies array to be: [reloadFeedbacks, seanceId]", {"range": "543", "text": "544"}, "Update the dependencies array to be: [messages.length, userLanguage, welcomeMessages]", {"range": "545", "text": "546"}, [5898, 5912], "[id, navigate, t]", [1508, 1519], "[fetchSeances, fetchSessionDetails, sessionId]", [2192, 2203], "[programId, t]", [1913, 1915], "[fetchUsers]", [1411, 1428], "[timeLeft, score, handleSubmit]", [3070, 3081], "[contenusByCourse, coursesByModule, programId]", [1223, 1234], "[reloadFeedbacks, sessionId]", [2428, 2430], "[fetchSessions]", [1041, 1043], "[t]", [2278, 2301], "[formateurId, loadStudents, seanceId]", [1791, 1801], "[reloadFeedbacks, seanceId]", [1685, 1699], "[messages.length, userLanguage, welcomeMessages]"]