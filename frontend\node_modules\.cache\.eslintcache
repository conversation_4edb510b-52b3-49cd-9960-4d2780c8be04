[{"C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\i18n\\index.js": "4", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\LoginPage.js": "5", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ForgetPasswordPage.js": "6", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\NotFoundPage.js": "7", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetPasswordPage.js": "8", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\HomePage.js": "9", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetSuccessPage.js": "10", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\FeedbackPage.js": "11", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\CoursesPage.js": "12", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProfilePage.js": "13", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentLandingPage.js": "14", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProgramsPage.js": "15", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ModulePage.js": "16", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentProgramPage.js": "17", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SeanceFormateurPage.js": "18", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ContenusPage.js": "19", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\WhiteboardPage.js": "20", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\EditProfilePage.js": "21", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionPage.js": "22", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\BuildProgramOverviewPage.js": "23", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\VerifyAccountPage.js": "24", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionDetail.js": "25", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Main.js": "26", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Auth.js": "27", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\UsersPages.js": "28", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddModuleView.js": "29", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddUserView.js": "30", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddProgramList.js": "31", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\UserList.js": "32", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddQuizForm.js": "33", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\PlayQuizPage.js": "34", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditProgramView.js": "35", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SeanceFormateurList.js": "36", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AnimerSeanceView.js": "37", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\BuildProgramView.js": "38", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFormateurView.js": "39", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ModuleList.js": "40", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditQuizForm.js": "41", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AdminDashboard.js": "42", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionFeedbackList.js": "43", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CreateurDashboard.js": "44", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtablissementDashboard.js": "45", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtudiantDashboard.js": "46", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\FormateurDashboard.js": "47", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\JitsiRoom.js": "48", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\index.js": "49", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastError.js": "50", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CourseList.js": "51", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastSuccess.js": "52", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddCourseView.js": "53", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ProgramList.js": "54", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ContenusList.js": "55", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Whiteboard.js": "56", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddContenusView.js": "57", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionList.js": "58", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\LanguageSelectorWithFlags.js": "59", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionView.js": "60", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScrollToTopButton.js": "61", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Session2ChatPopup.js": "62", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\toastError.js": "63", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\feedbackService.js": "64", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScoreReveal.js": "65", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\authUtils.js": "66", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\constants\\sideBarData.js": "67", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\FeedbackFormateur.js": "68", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\seancefeedbacklist.js": "69", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFeedback.js": "70", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\Chatbot.js": "71", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionFeedback.js": "72", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\QuickSuggestions.js": "73", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\languageService.js": "74", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\TestFeedback.js": "75"}, {"size": 610, "mtime": 1747831852695, "results": "76", "hashOfConfig": "77"}, {"size": 11585, "mtime": 1754071663554, "results": "78", "hashOfConfig": "77"}, {"size": 375, "mtime": 1745438071501, "results": "79", "hashOfConfig": "77"}, {"size": 656, "mtime": 1752859285013, "results": "80", "hashOfConfig": "77"}, {"size": 5622, "mtime": 1751990678109, "results": "81", "hashOfConfig": "77"}, {"size": 3723, "mtime": 1750871433201, "results": "82", "hashOfConfig": "77"}, {"size": 455, "mtime": 1750865324399, "results": "83", "hashOfConfig": "77"}, {"size": 6151, "mtime": 1750873153170, "results": "84", "hashOfConfig": "77"}, {"size": 1687, "mtime": 1753356431070, "results": "85", "hashOfConfig": "77"}, {"size": 694, "mtime": 1747594139572, "results": "86", "hashOfConfig": "77"}, {"size": 25936, "mtime": 1752342963093, "results": "87", "hashOfConfig": "77"}, {"size": 420, "mtime": 1747844523996, "results": "88", "hashOfConfig": "77"}, {"size": 15067, "mtime": 1753136360471, "results": "89", "hashOfConfig": "77"}, {"size": 2240, "mtime": 1750873583618, "results": "90", "hashOfConfig": "77"}, {"size": 491, "mtime": 1747844524003, "results": "91", "hashOfConfig": "77"}, {"size": 850, "mtime": 1750872193880, "results": "92", "hashOfConfig": "77"}, {"size": 4575, "mtime": 1750883025232, "results": "93", "hashOfConfig": "77"}, {"size": 3424, "mtime": 1753700299624, "results": "94", "hashOfConfig": "77"}, {"size": 422, "mtime": 1747844523996, "results": "95", "hashOfConfig": "77"}, {"size": 478, "mtime": 1751986578114, "results": "96", "hashOfConfig": "77"}, {"size": 55698, "mtime": 1751985929377, "results": "97", "hashOfConfig": "77"}, {"size": 1026, "mtime": 1750873436634, "results": "98", "hashOfConfig": "77"}, {"size": 7665, "mtime": 1753034959934, "results": "99", "hashOfConfig": "77"}, {"size": 15134, "mtime": 1752581112327, "results": "100", "hashOfConfig": "77"}, {"size": 4159, "mtime": 1750777638880, "results": "101", "hashOfConfig": "77"}, {"size": 19519, "mtime": 1753294211583, "results": "102", "hashOfConfig": "77"}, {"size": 153, "mtime": 1745582315206, "results": "103", "hashOfConfig": "77"}, {"size": 284, "mtime": 1750858318977, "results": "104", "hashOfConfig": "77"}, {"size": 2909, "mtime": 1750887085865, "results": "105", "hashOfConfig": "77"}, {"size": 11933, "mtime": 1752069953530, "results": "106", "hashOfConfig": "77"}, {"size": 1693, "mtime": 1750850985128, "results": "107", "hashOfConfig": "77"}, {"size": 25111, "mtime": 1751985930792, "results": "108", "hashOfConfig": "77"}, {"size": 11596, "mtime": 1750887959421, "results": "109", "hashOfConfig": "77"}, {"size": 6477, "mtime": 1750872851488, "results": "110", "hashOfConfig": "77"}, {"size": 12740, "mtime": 1749741251393, "results": "111", "hashOfConfig": "77"}, {"size": 5969, "mtime": 1753704624495, "results": "112", "hashOfConfig": "77"}, {"size": 22810, "mtime": 1753375441494, "results": "113", "hashOfConfig": "77"}, {"size": 8204, "mtime": 1750890990379, "results": "114", "hashOfConfig": "77"}, {"size": 5733, "mtime": 1752069593152, "results": "115", "hashOfConfig": "77"}, {"size": 3301, "mtime": 1752068170287, "results": "116", "hashOfConfig": "77"}, {"size": 11954, "mtime": 1752341829288, "results": "117", "hashOfConfig": "77"}, {"size": 22778, "mtime": 1753356431075, "results": "118", "hashOfConfig": "77"}, {"size": 27803, "mtime": 1754076584621, "results": "119", "hashOfConfig": "77"}, {"size": 22730, "mtime": 1753356431232, "results": "120", "hashOfConfig": "77"}, {"size": 10045, "mtime": 1753136361210, "results": "121", "hashOfConfig": "77"}, {"size": 9460, "mtime": 1753136361257, "results": "122", "hashOfConfig": "77"}, {"size": 14064, "mtime": 1753356431325, "results": "123", "hashOfConfig": "77"}, {"size": 665, "mtime": 1753136360410, "results": "124", "hashOfConfig": "77"}, {"size": 56, "mtime": 1750504542636, "results": "125", "hashOfConfig": "77"}, {"size": 751, "mtime": 1748635026074, "results": "126", "hashOfConfig": "77"}, {"size": 2964, "mtime": 1752068241528, "results": "127", "hashOfConfig": "77"}, {"size": 620, "mtime": 1747594139559, "results": "128", "hashOfConfig": "77"}, {"size": 1393, "mtime": 1750866339097, "results": "129", "hashOfConfig": "77"}, {"size": 3083, "mtime": 1751985930466, "results": "130", "hashOfConfig": "77"}, {"size": 4753, "mtime": 1751985930121, "results": "131", "hashOfConfig": "77"}, {"size": 10232, "mtime": 1753035018285, "results": "132", "hashOfConfig": "77"}, {"size": 4696, "mtime": 1751985929811, "results": "133", "hashOfConfig": "77"}, {"size": 25558, "mtime": 1753904297260, "results": "134", "hashOfConfig": "77"}, {"size": 7155, "mtime": 1751298764731, "results": "135", "hashOfConfig": "77"}, {"size": 5299, "mtime": 1750884972392, "results": "136", "hashOfConfig": "77"}, {"size": 897, "mtime": 1747061904678, "results": "137", "hashOfConfig": "77"}, {"size": 23129, "mtime": 1753356169008, "results": "138", "hashOfConfig": "77"}, {"size": 5376, "mtime": 1748517649309, "results": "139", "hashOfConfig": "77"}, {"size": 5485, "mtime": 1753034961627, "results": "140", "hashOfConfig": "77"}, {"size": 890, "mtime": 1749741250195, "results": "141", "hashOfConfig": "77"}, {"size": 4625, "mtime": 1753034977418, "results": "142", "hashOfConfig": "77"}, {"size": 2175, "mtime": 1753370053964, "results": "143", "hashOfConfig": "77"}, {"size": 12989, "mtime": 1753365132448, "results": "144", "hashOfConfig": "77"}, {"size": 12275, "mtime": 1753967365133, "results": "145", "hashOfConfig": "77"}, {"size": 23365, "mtime": 1753369624191, "results": "146", "hashOfConfig": "77"}, {"size": 10857, "mtime": 1753354385496, "results": "147", "hashOfConfig": "77"}, {"size": 55218, "mtime": 1754065007772, "results": "148", "hashOfConfig": "77"}, {"size": 1223, "mtime": 1751656822978, "results": "149", "hashOfConfig": "77"}, {"size": 2878, "mtime": 1751659811949, "results": "150", "hashOfConfig": "77"}, {"size": 1667, "mtime": 1754071617823, "results": "151", "hashOfConfig": "77"}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16w30kw", {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\App.js", ["377", "378"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\i18n\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ForgetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\NotFoundPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetSuccessPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\FeedbackPage.js", ["379", "380", "381"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\CoursesPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProfilePage.js", ["382"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentLandingPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProgramsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ModulePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentProgramPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SeanceFormateurPage.js", ["383"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ContenusPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\WhiteboardPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\EditProfilePage.js", ["384"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\BuildProgramOverviewPage.js", ["385"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\VerifyAccountPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Main.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\UsersPages.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddModuleView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddUserView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddProgramList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\UserList.js", ["386", "387", "388"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddQuizForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\PlayQuizPage.js", ["389", "390"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditProgramView.js", ["391"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SeanceFormateurList.js", ["392"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AnimerSeanceView.js", ["393", "394", "395"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\BuildProgramView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFormateurView.js", ["396"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ModuleList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditQuizForm.js", ["397", "398"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AdminDashboard.js", ["399", "400"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionFeedbackList.js", ["401", "402", "403"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CreateurDashboard.js", ["404", "405"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtablissementDashboard.js", ["406"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtudiantDashboard.js", ["407"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\FormateurDashboard.js", ["408"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\JitsiRoom.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastError.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CourseList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastSuccess.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddCourseView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ProgramList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ContenusList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Whiteboard.js", ["409", "410"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddContenusView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionList.js", ["411", "412", "413", "414", "415", "416", "417"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\LanguageSelectorWithFlags.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionView.js", ["418"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScrollToTopButton.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Session2ChatPopup.js", ["419", "420", "421"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\toastError.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\feedbackService.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScoreReveal.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\authUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\constants\\sideBarData.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\FeedbackFormateur.js", ["422"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\seancefeedbacklist.js", ["423"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFeedback.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\Chatbot.js", ["424"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionFeedback.js", ["425", "426"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\QuickSuggestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\languageService.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\TestFeedback.js", [], [], {"ruleId": "427", "severity": 1, "message": "428", "line": 4, "column": 10, "nodeType": "429", "messageId": "430", "endLine": 4, "endColumn": 24}, {"ruleId": "427", "severity": 1, "message": "431", "line": 55, "column": 8, "nodeType": "429", "messageId": "430", "endLine": 55, "endColumn": 21}, {"ruleId": "427", "severity": 1, "message": "432", "line": 19, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 19, "endColumn": 12}, {"ruleId": "427", "severity": 1, "message": "433", "line": 30, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 30, "endColumn": 10}, {"ruleId": "434", "severity": 1, "message": "435", "line": 202, "column": 5, "nodeType": "436", "messageId": "437", "endLine": 224, "endColumn": 6}, {"ruleId": "438", "severity": 1, "message": "439", "line": 170, "column": 6, "nodeType": "440", "endLine": 170, "endColumn": 20, "suggestions": "441"}, {"ruleId": "438", "severity": 1, "message": "442", "line": 42, "column": 6, "nodeType": "440", "endLine": 42, "endColumn": 17, "suggestions": "443"}, {"ruleId": "427", "severity": 1, "message": "444", "line": 171, "column": 19, "nodeType": "429", "messageId": "430", "endLine": 171, "endColumn": 23}, {"ruleId": "438", "severity": 1, "message": "445", "line": 69, "column": 6, "nodeType": "440", "endLine": 69, "endColumn": 17, "suggestions": "446"}, {"ruleId": "427", "severity": 1, "message": "447", "line": 37, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 37, "endColumn": 10}, {"ruleId": "438", "severity": 1, "message": "448", "line": 83, "column": 6, "nodeType": "440", "endLine": 83, "endColumn": 8, "suggestions": "449"}, {"ruleId": "427", "severity": 1, "message": "450", "line": 241, "column": 9, "nodeType": "429", "messageId": "430", "endLine": 241, "endColumn": 23}, {"ruleId": "438", "severity": 1, "message": "451", "line": 56, "column": 6, "nodeType": "440", "endLine": 56, "endColumn": 23, "suggestions": "452"}, {"ruleId": "427", "severity": 1, "message": "453", "line": 68, "column": 9, "nodeType": "429", "messageId": "430", "endLine": 68, "endColumn": 14}, {"ruleId": "438", "severity": 1, "message": "454", "line": 77, "column": 6, "nodeType": "440", "endLine": 77, "endColumn": 17, "suggestions": "455"}, {"ruleId": "427", "severity": 1, "message": "456", "line": 7, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 7, "endColumn": 8}, {"ruleId": "427", "severity": 1, "message": "457", "line": 47, "column": 10, "nodeType": "429", "messageId": "430", "endLine": 47, "endColumn": 17}, {"ruleId": "427", "severity": 1, "message": "458", "line": 56, "column": 10, "nodeType": "429", "messageId": "430", "endLine": 56, "endColumn": 29}, {"ruleId": "427", "severity": 1, "message": "459", "line": 56, "column": 31, "nodeType": "429", "messageId": "430", "endLine": 56, "endColumn": 53}, {"ruleId": "427", "severity": 1, "message": "460", "line": 18, "column": 10, "nodeType": "429", "messageId": "430", "endLine": 18, "endColumn": 13}, {"ruleId": "427", "severity": 1, "message": "433", "line": 14, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 14, "endColumn": 10}, {"ruleId": "427", "severity": 1, "message": "461", "line": 15, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 15, "endColumn": 10}, {"ruleId": "427", "severity": 1, "message": "462", "line": 22, "column": 8, "nodeType": "429", "messageId": "430", "endLine": 22, "endColumn": 21}, {"ruleId": "427", "severity": 1, "message": "463", "line": 23, "column": 8, "nodeType": "429", "messageId": "430", "endLine": 23, "endColumn": 19}, {"ruleId": "427", "severity": 1, "message": "464", "line": 1, "column": 17, "nodeType": "429", "messageId": "430", "endLine": 1, "endColumn": 26}, {"ruleId": "427", "severity": 1, "message": "433", "line": 14, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 14, "endColumn": 10}, {"ruleId": "438", "severity": 1, "message": "465", "line": 46, "column": 6, "nodeType": "440", "endLine": 46, "endColumn": 17, "suggestions": "466"}, {"ruleId": "427", "severity": 1, "message": "467", "line": 10, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 10, "endColumn": 9}, {"ruleId": "427", "severity": 1, "message": "468", "line": 75, "column": 10, "nodeType": "429", "messageId": "430", "endLine": 75, "endColumn": 15}, {"ruleId": "427", "severity": 1, "message": "469", "line": 31, "column": 20, "nodeType": "429", "messageId": "430", "endLine": 31, "endColumn": 31}, {"ruleId": "427", "severity": 1, "message": "470", "line": 18, "column": 8, "nodeType": "429", "messageId": "430", "endLine": 18, "endColumn": 22}, {"ruleId": "427", "severity": 1, "message": "471", "line": 16, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 16, "endColumn": 8}, {"ruleId": "427", "severity": 1, "message": "472", "line": 12, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 12, "endColumn": 9}, {"ruleId": "427", "severity": 1, "message": "473", "line": 19, "column": 8, "nodeType": "429", "messageId": "430", "endLine": 19, "endColumn": 21}, {"ruleId": "427", "severity": 1, "message": "474", "line": 30, "column": 8, "nodeType": "429", "messageId": "430", "endLine": 30, "endColumn": 27}, {"ruleId": "427", "severity": 1, "message": "475", "line": 43, "column": 10, "nodeType": "429", "messageId": "430", "endLine": 43, "endColumn": 33}, {"ruleId": "427", "severity": 1, "message": "476", "line": 43, "column": 35, "nodeType": "429", "messageId": "430", "endLine": 43, "endColumn": 61}, {"ruleId": "427", "severity": 1, "message": "477", "line": 44, "column": 10, "nodeType": "429", "messageId": "430", "endLine": 44, "endColumn": 40}, {"ruleId": "427", "severity": 1, "message": "478", "line": 44, "column": 42, "nodeType": "429", "messageId": "430", "endLine": 44, "endColumn": 75}, {"ruleId": "438", "severity": 1, "message": "479", "line": 71, "column": 6, "nodeType": "440", "endLine": 71, "endColumn": 8, "suggestions": "480"}, {"ruleId": "427", "severity": 1, "message": "481", "line": 162, "column": 9, "nodeType": "429", "messageId": "430", "endLine": 162, "endColumn": 30}, {"ruleId": "438", "severity": 1, "message": "439", "line": 35, "column": 6, "nodeType": "440", "endLine": 35, "endColumn": 8, "suggestions": "482"}, {"ruleId": "427", "severity": 1, "message": "483", "line": 9, "column": 10, "nodeType": "429", "messageId": "430", "endLine": 9, "endColumn": 14}, {"ruleId": "427", "severity": 1, "message": "484", "line": 9, "column": 16, "nodeType": "429", "messageId": "430", "endLine": 9, "endColumn": 19}, {"ruleId": "427", "severity": 1, "message": "485", "line": 40, "column": 23, "nodeType": "429", "messageId": "430", "endLine": 40, "endColumn": 37}, {"ruleId": "438", "severity": 1, "message": "486", "line": 76, "column": 6, "nodeType": "440", "endLine": 76, "endColumn": 29, "suggestions": "487"}, {"ruleId": "438", "severity": 1, "message": "488", "line": 52, "column": 6, "nodeType": "440", "endLine": 52, "endColumn": 16, "suggestions": "489"}, {"ruleId": "438", "severity": 1, "message": "490", "line": 35, "column": 6, "nodeType": "440", "endLine": 35, "endColumn": 20, "suggestions": "491"}, {"ruleId": "427", "severity": 1, "message": "433", "line": 30, "column": 3, "nodeType": "429", "messageId": "430", "endLine": 30, "endColumn": 10}, {"ruleId": "434", "severity": 1, "message": "435", "line": 193, "column": 5, "nodeType": "436", "messageId": "437", "endLine": 220, "endColumn": 6}, "no-unused-vars", "'useTranslation' is defined but never used.", "Identifier", "unusedVar", "'SessionDetail' is defined but never used.", "'FormGroup' is defined but never used.", "'Divider' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["492"], "React Hook useEffect has missing dependencies: 'fetchSeances' and 'fetchSessionDetails'. Either include them or remove the dependency array.", ["493"], "'gray' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 't'. Either include it or remove the dependency array.", ["494"], "'Refresh' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["495"], "'getAvatarColor' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["496"], "'total' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'contenusByCourse' and 'coursesByModule'. Either include them or remove the dependency array.", ["497"], "'Stack' is defined but never used.", "'prevTab' is assigned a value but never used.", "'showFeedbackSidebar' is assigned a value but never used.", "'setShowFeedbackSidebar' is assigned a value but never used.", "'Eye' is defined but never used.", "'Tooltip' is defined but never used.", "'EventBusyIcon' is defined but never used.", "'ArchiveIcon' is defined but never used.", "'useEffect' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'reloadFeedbacks'. Either include it or remove the dependency array.", ["498"], "'Avatar' is defined but never used.", "'error' is assigned a value but never used.", "'setStudents' is assigned a value but never used.", "'TrendingUpIcon' is defined but never used.", "'Paper' is defined but never used.", "'Button' is defined but never used.", "'ColorLensIcon' is defined but never used.", "'SessionFeedbackList' is defined but never used.", "'openSessionFeedbackList' is assigned a value but never used.", "'setOpenSessionFeedbackList' is assigned a value but never used.", "'selectedSessionForFeedbackList' is assigned a value but never used.", "'setSelectedSessionForFeedbackList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSessions'. Either include it or remove the dependency array.", ["499"], "'handleDownloadPreview' is assigned a value but never used.", ["500"], "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'setShowTooltip' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStudents'. Either include it or remove the dependency array.", ["501"], "React Hook useEffect has a missing dependency: 'reloadFeedbacks'. Either include it or remove the dependency array.", ["502"], "React Hook useEffect has missing dependencies: 'messages.length' and 'welcomeMessages'. Either include them or remove the dependency array.", ["503"], {"desc": "504", "fix": "505"}, {"desc": "506", "fix": "507"}, {"desc": "508", "fix": "509"}, {"desc": "510", "fix": "511"}, {"desc": "512", "fix": "513"}, {"desc": "514", "fix": "515"}, {"desc": "516", "fix": "517"}, {"desc": "518", "fix": "519"}, {"desc": "520", "fix": "521"}, {"desc": "522", "fix": "523"}, {"desc": "524", "fix": "525"}, {"desc": "526", "fix": "527"}, "Update the dependencies array to be: [id, navigate, t]", {"range": "528", "text": "529"}, "Update the dependencies array to be: [fetchSeances, fetchSessionDetails, sessionId]", {"range": "530", "text": "531"}, "Update the dependencies array to be: [programId, t]", {"range": "532", "text": "533"}, "Update the dependencies array to be: [fetchUsers]", {"range": "534", "text": "535"}, "Update the dependencies array to be: [timeLeft, score, handleSubmit]", {"range": "536", "text": "537"}, "Update the dependencies array to be: [contenusByCourse, coursesByModule, programId]", {"range": "538", "text": "539"}, "Update the dependencies array to be: [reloadFeedbacks, sessionId]", {"range": "540", "text": "541"}, "Update the dependencies array to be: [fetchSessions]", {"range": "542", "text": "543"}, "Update the dependencies array to be: [t]", {"range": "544", "text": "545"}, "Update the dependencies array to be: [formateurId, loadStudents, seanceId]", {"range": "546", "text": "547"}, "Update the dependencies array to be: [reloadFeedbacks, seanceId]", {"range": "548", "text": "549"}, "Update the dependencies array to be: [messages.length, userLanguage, welcomeMessages]", {"range": "550", "text": "551"}, [5898, 5912], "[id, navigate, t]", [1508, 1519], "[fetchSeances, fetchSessionDetails, sessionId]", [2192, 2203], "[programId, t]", [1913, 1915], "[fetchUsers]", [1411, 1428], "[timeLeft, score, handleSubmit]", [3070, 3081], "[contenusByCourse, coursesByModule, programId]", [1272, 1283], "[reloadFeedbacks, sessionId]", [2428, 2430], "[fetchSessions]", [1041, 1043], "[t]", [2278, 2301], "[formateurId, loadStudents, seanceId]", [1791, 1801], "[reloadFeedbacks, seanceId]", [1685, 1699], "[messages.length, userLanguage, welcomeMessages]"]